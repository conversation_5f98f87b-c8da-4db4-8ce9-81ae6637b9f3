import Router from 'koa-router';
import bodyParser from 'koa-body';
import proxy from 'koa-better-http-proxy';
import { keyHelper, tokenHelper, routingHelper } from '../services';
import { tokenVerify, swaggerDocFilter } from '../middlewares';
import { environment } from '../environment';
import agent from 'superagent';

const accessStore: { token?: string; expires?: number } = {};

export function ApiRouter() {
  const router = new Router();

  /**
   * 根路径请求
   */
  router.get('/', async (ctx, next) => {
    let content = `<div>
  <p>Welcome to BPM Api Gateway!</p>
  <p>Key registear path: /app-key</p>
  <p>Access token  path: /token</p>
  <p>Access header name: 'access_token'</p>
  </div>`;
    const allKeys = await routingHelper.all();
    allKeys.forEach(key => (content += `<br/><a href="/api/${key}/swagger/index.html">Go to [${key}] service doc.</a>`));
    ctx.body = content;
    ctx.type = 'html';
  });

  /**
   * 注册同时申请app key
   */
  router.post('/app-key', bodyParser(), async (ctx, next) => {
    const fields = ctx.request.body;
    if (!fields) {
      ctx.status = 400;
      ctx.body = 'invalid fields';
    } else {
      await keyHelper
        .generate(fields)
        .then(result => {
          ctx.body = result;
        })
        .catch(err => {
          if (typeof err === 'string' || !err.status) {
            ctx.status = 400;
            ctx.body = err;
          } else {
            ctx.status = err.status;
            ctx.body = err.message;
          }
        });
    }
  });

  /**
   * 获取token
   */
  router.post('/token', bodyParser(), async (ctx, next) => {
    const fields = ctx.request.body;
    if (fields && fields['app-key'] && fields['app-secret']) {
      ctx.body = tokenHelper.createBySecret(fields['app-key'], fields['app-secret']);
    } else if (fields && fields['app-key'] && fields['user-name'] && fields['user-pass']) {
      ctx.body = tokenHelper.createByNamePass(fields['app-key'], fields['user-name'], fields['user-pass']);
    } else {
      ctx.throw(400, 'Invalid authorize fields.');
    }
  });

  /**
   * 验证token
   */
  router.post('/token-verify', bodyParser(), async (ctx, next) => {
    const fields = ctx.request.body;
    if (fields && fields['token']) {
      await tokenHelper
        .verify(fields['token'])
        .then(() => {
          ctx.body = 'Token valid.';
        })
        .catch(err => {
          ctx.throw(404, 'Token invalid.');
        });
    } else {
      ctx.throw(400, 'Invalid token fields.');
    }
  });

  router.all(
    '/api/*',
    swaggerDocFilter(),
    proxy('0', {
      limit: environment.limit,
      proxyReqOptDecorator: async (options, ctx) => {
        await routingHelper
          .map(ctx.url.substr(4))
          .then((ret: any) => {
            options.hostname = ret.host;
            options.port = ret.port;
            ctx.url = ret.url === '/' ? '/swagger/index.html' : ret.url;
          })
          .catch(err => {
            ctx.throw(404, 'Host not found.');
          });
        return options;
      },
      userResDecorator: async (proxyRes, proxyResData, ctx): Promise<any> => {
        return new Promise(resolve => {
          const rootPath = ctx.originalUrl.match(/^(\/api\/[^\/]+)\/?.*/)[1];
          if (ctx.path.endsWith('.html')) {
            const html = proxyResData.toString('utf8').replace(/\"\.\/|\"\/swagger\//g, `"${rootPath}/swagger/`);
            proxyResData = Buffer.from(html, 'utf8');
          } else if (ctx.path.endsWith('swagger.json')) {
            const json = JSON.parse(proxyResData.toString('utf8'));
            if (json && json.paths) {
              const newPaths: any = {};
              Object.keys(json.paths).forEach(key => {
                newPaths[rootPath + key] = json.paths[key];
              });
              json.paths = newPaths;
            }
            proxyResData = Buffer.from(JSON.stringify(json), 'utf8');
          }
          setTimeout(() => {
            resolve(proxyResData);
          });
        });
      },
    }),
    async (ctx, next) => {
      // 不再继续处理
    }
  );

  /**
   * 请求分发
   */
  router.all(
    '/*',
    tokenVerify(),
    proxy('0', {
      limit: environment.limit,
      timeout: environment.proxyTimeout,
      proxyReqOptDecorator: async (options, ctx) => {
        if (ctx.is('text/*')) {
          options.headers['content-type'] = 'application/json';
        }
        // 调用集成中心的api需要添加认证
        console.log(`🚀 ~ accessStore`, accessStore);
        if (ctx.url.toLocaleLowerCase().substring(1).startsWith(environment.OcelotAuthOption.UrlPrefix)) {
          if (!accessStore || !accessStore.expires || accessStore.expires < new Date().getTime()) {
            await agent
              .post(environment.OcelotAuthOption.Host)
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .send({
                client_id: environment.OcelotAuthOption.UserName,
                client_secret: environment.OcelotAuthOption.Password,
                grant_type: environment.OcelotAuthOption.GrantType,
              })
              .then((res: any) => {
                const { access_token, expires_in } = res.body;
                accessStore.token = access_token;
                accessStore.expires = new Date().getTime() + expires_in * 1000;
              })
              .catch(ex => {
                ctx.throw(502, 'Failed to access.');
              });
          }
          options.headers['Authorization'] = 'Bearer ' + accessStore.token;
        }
        await routingHelper
          .map(ctx.url)
          .then((ret: any) => {
            options.hostname = ret.host;
            options.port = ret.port;
            ctx.url = ret.url;
          })
          .catch(err => {
            ctx.throw(404, 'Host not found.');
          });
        return options;
      },
    }),
    async (ctx, next) => {
      // 不再继续处理
    }
  );

  return router.routes();
}

// export const routes = router.routes();
