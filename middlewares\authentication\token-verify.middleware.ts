import Koa from 'koa';
import { token<PERSON>elper } from '../../services/token.service';

export function tokenVerify(): any {
  return async (ctx: Koa.Context, next: () => Promise<any>) => {
    let accessToken = ctx.header['access_token'];
    if (!accessToken) {
      ctx.throw(401, 'Request must contains header[access_token].');
    } else {
      await tokenHelper
        .verify(accessToken)
        .then(() => {
          // accessed
        })
        .catch(err => {
          ctx.throw(401, err);
        });
      await next();
    }
  };
}
