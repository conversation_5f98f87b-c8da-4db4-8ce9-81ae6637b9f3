stages:
  - lint
  - build
  - deploy
  - release

before_script:
  - if [[ -z $PROJECT_NAME ]]; then echo 'Missing Secret Variable -> $PROJECT_NAME' && exit 10; fi
  - if [[ -z $PROJECT_PORT ]]; then echo 'Missing Secret Variable -> $PROJECT_PORT' && exit 11; fi
  - HARBOR=*************:8858/matrix

lint_task:
  stage: lint
  except:
    - tags
    - develop
    - master
  script:
    - yarn add tslint typescript --dev --prefer-offline
    - yarn run lint

build_task:
  stage: build
  except:
    - tags
    - develop
    - master
  script:
    - yarn install --prefer-offline
    - yarn run build

dev_deploy:
  stage: deploy
  only:
    - develop
  script:
    - yarn install --prefer-offline
    - yarn run build
    - sed -i -e "s/3001/$PROJECT_PORT/g" dist/config.json
    - cp package.json dist
    - cp package-lock.json dist
    - tar -cvzf /tmp/$PROJECT_NAME.tgz -C dist .
    - deploy_run "rm -rf /var/www/boost/$PROJECT_NAME/tmp"
    - deploy_run "mkdir /var/www/boost/$PROJECT_NAME/tmp"
    - deploy_copy /tmp/$PROJECT_NAME.tgz /var/www/boost/$PROJECT_NAME/tmp
    - deploy_run "cd /var/www/boost/$PROJECT_NAME/tmp && tar -xvzf $PROJECT_NAME.tgz"
    - deploy_run "cd /var/www/boost/$PROJECT_NAME/tmp && rm $PROJECT_NAME.tgz"
    - deploy_run "cd /var/www/boost/$PROJECT_NAME/tmp && yarn install --prefer-offline"
    - deploy_run "rm -rf /var/www/boost/$PROJECT_NAME/src"
    - deploy_run "mv /var/www/boost/$PROJECT_NAME/tmp /var/www/boost/$PROJECT_NAME/src"
    - deploy_run "pm2 restart $PROJECT_NAME"

release_task:
  stage: release
  only:
    - tags
  script:
    - deploy_run "cd /var/www/boost/$PROJECT_NAME/src && echo 'FROM node:12\n\nWORKDIR /app\n\nCOPY package*.json ./\n\nCOPY . .\n\nEXPOSE 20050\nCMD [ \"node\", \"app.js\" ]'>Dockerfile && docker build -t $PROJECT_NAME:$CI_COMMIT_TAG  ."
    - deploy_run "docker tag $PROJECT_NAME:$CI_COMMIT_TAG  $HARBOR/$PROJECT_NAME:$CI_COMMIT_TAG"
    - deploy_run "docker push $HARBOR/$PROJECT_NAME:$CI_COMMIT_TAG"
    - deploy_run "docker rmi $HARBOR/$PROJECT_NAME:$CI_COMMIT_TAG && docker rmi $PROJECT_NAME:$CI_COMMIT_TAG"