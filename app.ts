import Koa from 'koa';
import logger from 'koa-logger';
import serve from 'koa-static';
import { environment, nacos } from './environment';
import { ApiRouter } from './routes';

const app = new Koa();

nacos().then(() => {
  app.use(logger());
  app.use(serve(__dirname + '/static'));
  app.use(ApiRouter());

  if (!module.parent) {
    app.listen(environment.port, () => {
      console.log(`[*] listening on :${environment.port}`);
    });
  }
});
