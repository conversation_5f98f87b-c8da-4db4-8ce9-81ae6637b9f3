import redis from 'redis';
import { environment } from '../environment';

function connect(callback: (client: redis.RedisClient) => void) {
  const client = redis.createClient({
    host: environment.redis.host,
    port: environment.redis.port,
    db: environment.redis.db,
    password: environment.redis.password,
  });
  callback(client);
}

export const redisHelper = {
  /**
   * 设置单个String类型
   */
  setSingle: (key: string, value: any, expires?: number): Promise<string> => {
    if (typeof value !== 'string') {
      value = JSON.stringify(value);
    }
    return new Promise((resolve, reject) => {
      connect(client => {
        const cb = (err: any) => {
          client.end(true);
          if (err) {
            reject(err);
          } else {
            resolve(key);
          }
        };
        if (expires) {
          client.set(key, value, 'EX', expires, cb);
        } else {
          client.set(key, value, cb);
        }
      });
    });
  },
  /**
   * 获取单个String类型
   */
  getSingle: (key: string): Promise<string> => {
    return new Promise((resolve, reject) => {
      connect(client => {
        client.get(key, (err, data) => {
          client.end(true);
          if (err) {
            reject(err);
          } else {
            resolve(data);
          }
        });
      });
    });
  },
  /**
   * 设置Hash的一个或多个值
   */
  setHash: (key: string, hashtable: any): Promise<string> => {
    return new Promise((resolve, reject) => {
      connect(client => {
        client.hmset(key, hashtable, (err, data) => {
          client.end(true);
          if (err) {
            reject(err);
          } else {
            resolve(key);
          }
        });
      });
    });
  },
  /**
   * 获取Hash中的一对键值
   */
  getOneHash: (key: string, skey: string): Promise<string> => {
    return new Promise((resolve, reject) => {
      connect(client => {
        client.hget(key, skey, (err, data) => {
          client.end(true);
          if (err) {
            reject(err);
          } else {
            resolve(data);
          }
        });
      });
    });
  },
  /**
   * 获取Hash中的所有键值
   */
  getAllHash: (key: string): Promise<{ [key: string]: string }> => {
    return new Promise((resolve, reject) => {
      connect(client => {
        client.hgetall(key, (err, data) => {
          client.end(true);
          if (err) {
            reject(err);
          } else {
            resolve(data);
          }
        });
      });
    });
  },
  /**
   * 检查Hash是否存在
   */
  hExists: (key: string, skey: string): Promise<boolean> => {
    return new Promise((resolve, reject) => {
      connect(client => {
        client.hexists(key, skey, (err, num) => {
          client.end(true);
          if (err) {
            reject(err);
          } else {
            resolve(!!num);
          }
        });
      });
    });
  },
  /**
   * 删除一个或多个键
   */
  delete: (key: string): Promise<string> => {
    return new Promise((resolve, reject) => {
      connect(client => {
        client.del(key, err => {
          client.end(true);
          if (err) {
            reject(err);
          } else {
            resolve(key);
          }
        });
      });
    });
  },
};
