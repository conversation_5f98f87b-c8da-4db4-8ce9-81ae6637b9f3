import crypto from 'crypto';
import { redisHelper } from './redis.service';
import { AccessInfo } from './types';

const accountReg = /^[0-9a-z]{6,32}$/i;

export const keyHelper = {
  /**
   * 根据主机地址生成app key
   */
  generate: (accessInfo: AccessInfo) => {
    return new Promise((resolve, reject) => {
      if (
        !accessInfo.account ||
        !accountReg.test(accessInfo.account) ||
        !accessInfo.password ||
        !accessInfo.hosts ||
        (Array.isArray(accessInfo.hosts) && accessInfo.hosts.length < 1)
      ) {
        // 账号或密码不符合要求的，没有设置host的，返回
        reject('invalid fields');
        return;
      }
      redisHelper
        .hExists(accessInfo.account, 'account')
        .then(exists => {
          if (exists) {
            reject('account already exists');
          } else {
            const appKey = crypto.randomBytes(16).toString('hex');
            const appSecret = crypto.randomBytes(32).toString('hex');
            redisHelper
              .setHash(accessInfo.account, {
                account: accessInfo.account,
                name: accessInfo.name || '',
                password: accessInfo.password,
                hosts: accessInfo.hosts.length > 0 ? accessInfo.hosts.toString() : accessInfo.hosts,
                appKey: appKey,
                appSecret: appSecret,
              })
              .then(() => {
                resolve({ appKey: appKey, appSecret: appSecret });
              })
              .catch(err => {
                reject(err);
              });
          }
        })
        .catch(err => {
          reject(err);
        });
    });
  },
};
