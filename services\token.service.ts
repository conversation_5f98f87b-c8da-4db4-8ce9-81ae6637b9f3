import jwt from 'jsonwebtoken';
import { redisHelper } from './redis.service';
import { environment } from '../environment';

export const tokenHelper = {
  /**
   * 通过AppKey和AppSecret生成Token
   *
   * @param appKey 接入方App Key
   * @param appSecret 接入方App Secret
   */
  createBySecret: (appKey: string, appSecret: string) => {
    const token = jwt.sign({ appKey: appKey }, environment.token.secret, { expiresIn: environment.token.expires });
    if (environment.token.whiteList.indexOf(appKey) < 0) {
      redisHelper.setSingle(appKey, token, environment.token.expires);
    }
    return { token: token, expiresIn: environment.token.expires };
  },
  /**
   * 通过AppKey和用户账号密码生成Token
   *
   * @param appKey 接入方App Key
   * @param userName 用户登录名
   * @param userPass 用户登录密码
   */
  createByNamePass: (appKey: string, userName: string, userPass: string) => {
    const token = jwt.sign({ appKey: appKey }, environment.token.secret, { expiresIn: environment.token.expires });
    if (environment.token.whiteList.indexOf(appKey) < 0) {
      redisHelper.setSingle(appKey, token, environment.token.expires);
    }
    return { token: token, expiresIn: environment.token.expires };
  },
  /**
   * 验证token有效性
   *
   * @param token JWT生成的token
   */
  verify: (token: string) => {
    return new Promise((resolve, reject) => {
      if (environment.token.forJobs.indexOf(token) > -1) {
        // job token直接放行
        resolve();
      } else {
        jwt.verify(token, environment.token.secret, (err: any, decoded: any) => {
          if (decoded) {
            const appKey = decoded.appKey;
            if (environment.token.whiteList.indexOf(appKey) > -1) {
              resolve();
            } else {
              redisHelper
                .getSingle(appKey)
                .then(tokenInRedis => {
                  if (tokenInRedis !== token) {
                    reject('Token invalid or expired.');
                  } else {
                    resolve();
                  }
                })
                .catch(() => {
                  reject('Token invalid or expired.');
                });
            }
          } else {
            reject('Token invalid or expired.');
          }
        });
      }
    });
  },
};
