import { environment } from '../environment';
import { redisHelper } from './redis.service';

const prefixReg = /^\/?([a-z0-9_-]+)(\/.*)/i;
const routesKey = '_routes';

export const routingHelper = {
  /**
   * 根据path匹配api地址
   */
  map: (reqUrl: string): Promise<any> => {
    return new Promise((resolve, reject) => {
      if (!prefixReg.test(reqUrl)) {
        // 判断请求路径是否在路由表中
        reject({
          status: 404,
          message: 'Invalid path.',
        });
      } else {
        // RegExp取Group必须放在Promise外，
        // 否则进入异步线程无法获取正确的值
        const api = RegExp.$1;
        const originUrl = RegExp.$2;
        if (environment.routeType === 'redis') {
          redisHelper.getOneHash(routesKey, api).then((address: string) => {
            if (address) {
              const [host, port] = address.split(':');
              resolve({
                host: host,
                port: port,
                url: originUrl,
              });
            } else {
              reject({
                status: 404,
                message: 'Host not found.',
              });
            }
          });
        } else {
          const routeKey = Object.keys(environment.routes).find(k => k === api);
          const address = routeKey ? environment.routes[routeKey] : undefined;
          if (address) {
            const [host, port] = address.split(':');
            resolve({
              host: host,
              port: port,
              url: originUrl,
            });
          } else {
            reject({
              status: 404,
              message: 'Host not found.',
            });
          }
        }
      }
    });
  },
  all: (): Promise<string[]> => {
    return new Promise((resolve, reject) => {
      redisHelper.getAllHash(routesKey).then(hash => {
        resolve(Object.keys(hash));
      });
    });
  },
};
