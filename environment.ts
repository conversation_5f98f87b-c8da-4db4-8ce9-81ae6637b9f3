import config from './config.json';
import devConfig from './config.dev.json';
import { NacosConfigClient } from 'nacos';

const isDevelopment = process.env.NODE_ENV === 'development';

export let environment: { [key: string]: any | any[] } = {};
export const configJson: { [key: string]: any | any[] } = {
  isDevelopment: isDevelopment,
  ...(isDevelopment ? devConfig : config),
};

const configClient = new NacosConfigClient({
  serverAddr: configJson.nacos.serverAddr,
  namespace: configJson.nacos.namespace,
});

export async function nacos() {
  if (isDevelopment) {
    environment = configJson;
  } else {
    await configClient.getConfig(configJson.nacos.dataid, configJson.nacos.group).then((data) => {
      environment = { isDevelopment: isDevelopment, ...JSON.parse(data) };
    });
  }
}
