{"version": "0.0.1", "description": "Movitech api gate way. Includes auth, cache, format, limit", "repository": {}, "scripts": {"tsc": "tsc", "rimraf": "<PERSON><PERSON><PERSON>", "npm-run-all": "npm-run-all", "lint": "tslint -c ./tslint.json ./**/*.ts", "build": "npm-run-all \"rimraf dist\" tsc", "start:dev": "nodemon"}, "dependencies": {"@types/http-assert": "1.5.1", "jsonwebtoken": "^8.1.0", "koa": "~2.5.2", "koa-better-http-proxy": "~0.2.4", "koa-body": "~4.0.4", "koa-logger": "~3.2.0", "koa-router": "~7.4.0", "koa-static": "~5.0.0", "nacos": "^1.1.0", "redis": "^2.8.0", "superagent": "^3.8.3"}, "devDependencies": {"@types/jsonwebtoken": "^8.5.0", "@types/koa": "~2.0.47", "@types/koa-logger": "~3.1.1", "@types/koa-router": "~7.0.35", "@types/koa-static": "~4.0.0", "@types/redis": "^2.8.27", "@types/swagger-ui-dist": "^3.30.0", "@types/superagent": "~3.8.4", "nodemon": "^1.14.3", "npm-run-all": "~4.1.5", "rimraf": "~2.6.3", "ts-node": "~7.0.1", "tslint": "~5.12.1", "typescript": "~3.3.3"}, "license": "MIT"}