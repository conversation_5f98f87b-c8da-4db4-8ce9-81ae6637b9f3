{"port": 20050, "limit": "200mb", "proxyTimeout": 60000, "routeType": "redis", "routes": {}, "redis": {"host": "*************", "port": 6379, "db": 8, "password": "7Qx0YFWyUDsbjzBv"}, "token": {"expires": 43200, "secret": "movitech-jwt", "whiteList": ["799e6e124ad95e09b055ae8c8cc53d7f"], "forJobs": ["nCBYEUvkcgFFGFCRcHm3Lizu"]}, "OcelotAuthOption": {"Host": "http://*************:32065/connect/Token", "UserName": "Admin", "Password": "12345678", "GrantType": "client_credentials", "UrlPrefix": "integration-ocelot"}}